import React from 'react';
import styles from './style.module.css';
import Logo from './logo/Logo';
import HeaderButton from './headerButton/HeaderButton';

interface HeaderProps {
  setSituation: (situation: any) => void;
  showConstellationSettingPanel: () => void;
  handleBeamAdjust?: () => void;
  satelliteAdd: () => void;
  groundStationAdd: () => void;
  showMappingModal: () => void;
  showUserPreferences: () => void;
}

const Header: React.FC<HeaderProps> = ({
  setSituation,
  showConstellationSettingPanel,
  handleBeamAdjust,
  satelliteAdd,
  groundStationAdd,
  showMappingModal,
  showUserPreferences
}) => {
  const handleSituationClick = () => {
    setSituation({
      satellite: true,
      communicate: false,
      basestation: false,
      resource: false,
      business: false
    });
  };

  return (
    <header className={styles.header}>
      {/* header-background */}
      <div className={styles.headerBackground}>
        {/* 槽 1：Logo */}
        <div className={styles.logoBlock}>
          <Logo />
        </div>

        {/* 槽 2-3：前两按钮 */}
        <div className={styles.leftButtons}>
          {/* <div className={styles.slot}>
            <HeaderButton
              text="星座运行态势"
              onClick={handleSituationClick}
            />
          </div> */}

          {/* <div className={styles.slot}>
            <HeaderButton
              text="信道模型设计"
              onClick={showUserPreferences}
            />
          </div> */}

          <div className={styles.slot}>
            <HeaderButton
              text="星座选择配置"
              onClick={showConstellationSettingPanel}
            />
          </div>
        </div>

        {/* 槽 4：Name 栏在headerBackground中的占位元素*/}
        <div className={styles.backTitleBlock} />

        {/* 槽 5：右侧按钮 */}
        {/* <div className={styles.rightButtons}>
          <div className={styles.slot}>
            <HeaderButton
              text="修改波束尺寸"
              onClick={handleBeamAdjust}
            />
          </div>
        </div> */}
        <div className={styles.rightButtons}>
          <div className={styles.slot}>
            <HeaderButton
              text="平台设计"
              onClick={satelliteAdd}
            />
          </div>
          {/* <div className={styles.slot}>
            <HeaderButton
              text="地面站设计"
              onClick={groundStationAdd}
            />
          </div> */}
          <div className={styles.slot}>
            <HeaderButton
              text="映射图"
              onClick={showMappingModal}
            />
          </div>
        </div> 
      </div>

      {/* Title 栏：真正的显示平台名的地方 */}
      <div className={styles.titleBlock}>
        <div className={styles.text}>
          <h1>
            星地智生一体化数字孪生系统
          </h1>
        </div>
      </div>
    </header>
  );
};

export default Header; 